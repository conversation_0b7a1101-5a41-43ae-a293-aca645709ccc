import { Metadata } from 'next';
import { getPublicTherapistById, TherapistData } from '@/services/public-calendar.service';

type Props = {
  params: { id: string };
  children: React.ReactNode;
};

export async function generateMetadata({ params }: Props): Promise<Metadata> {
  const { id } = params;

  try {
    // Fetch therapist data for meta tags
    const response = await getPublicTherapistById(id);
    const therapistData = response as unknown as TherapistData;

    // Helper function to safely extract string values
    const safeString = (value: string | Record<string, unknown> | null | undefined): string => {
      if (typeof value === 'string') return value;
      return '';
    };

    // Helper function to safely extract string from array or string
    const safeStringFromArray = (value: string[] | string | null | undefined): string => {
      if (typeof value === 'string') return value;
      if (Array.isArray(value) && value.length > 0) return value[0];
      return '';
    };

    const therapistName = safeString(therapistData?.name) || 'Therapist';

    // Use practicingTitle from verificationDetails as the primary designation
    const verificationDetails = therapistData?.verificationDetails as { practicingTitle?: string } | undefined;
    const therapistDesignation = verificationDetails?.practicingTitle || safeString(therapistData?.designation) || 'Licensed Therapist';

    const therapistLocation = safeStringFromArray(therapistData?.location) || 'Online';

    // Handle profile image with better fallback logic
    let profileImage = safeString(therapistData?.profilePicUrl);

    // If no profile image or it's not a valid URL, use static fallback
    if (!profileImage || profileImage === 'null' || profileImage === 'undefined') {
      profileImage = '/assets/images/newHome/therapist-profile-logo.png';
    } else if (!profileImage.startsWith('http')) {
      // If it's a relative path, make it absolute
      const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || 'https://app.thoughtpudding.com';
      profileImage = profileImage.startsWith('/') ? `${baseUrl}${profileImage}` : `${baseUrl}/${profileImage}`;
    }

    const therapyApproach = safeString(therapistData?.practiceApproach) || 'Professional therapy services';

    // Create the page URL
    const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || 'https://app.thoughtpudding.com';
    const pageUrl = `${baseUrl}/clients/${id}`;

    // Create the title and description according to the new template
    const title = `Book therapy with ${therapistName} - ${therapistDesignation}`;

    // Use the practiceApproach (How therapy works with me) as the main description
    // Limit to a reasonable length for social media previews
    const description = therapyApproach.length > 160
      ? `${therapyApproach.substring(0, 157)}...`
      : therapyApproach;

    // Ensure profile image has full URL
    const fullProfileImageUrl = profileImage.startsWith('http')
      ? profileImage
      : profileImage.startsWith('/')
        ? `${baseUrl}${profileImage}`
        : `${baseUrl}/${profileImage}`;

    // Use hardcoded image URL for testing social media sharing
    const hardcodedImageUrl = 'https://thoughtpudding-public.s3.ap-south-1.amazonaws.com/1748437689488_pexels-sulimansallehi-1704488.jpg';

    // Create dynamic Open Graph image URL with cache-busting
    const cacheVersion = '2025-01-29-v6'; // Updated to force cache refresh for profile image with better fallback
    const ogImageUrl = `${baseUrl}/api/og?name=${encodeURIComponent(therapistName)}&title=${encodeURIComponent(therapistDesignation)}&location=${encodeURIComponent(therapistLocation)}&image=${encodeURIComponent(hardcodedImageUrl)}&v=${cacheVersion}`;

    return {
      title,
      description,
      keywords: [
        'therapy',
        'counseling',
        'mental health',
        'therapist',
        therapistName,
        therapistDesignation,
        'online therapy',
        'book session',
        'thought pudding'
      ],
      authors: [{ name: 'Thought Pudding' }],
      creator: 'Thought Pudding',
      publisher: 'Thought Pudding',

      // Open Graph tags for social media sharing
      openGraph: {
        title,
        description,
        url: pageUrl,
        siteName: 'Thought Pudding',
        type: 'profile',
        locale: 'en_US',
        images: [
          {
            url: ogImageUrl,
            secureUrl: ogImageUrl,
            width: 1200,
            height: 630,
            alt: `Book therapy with ${therapistName} - ${therapistDesignation}`,
            type: 'image/png',
          },
          {
            url: hardcodedImageUrl,
            secureUrl: hardcodedImageUrl,
            width: 400,
            height: 400,
            alt: `${therapistName} - ${therapistDesignation}`,
            type: 'image/jpeg',
          },
          {
            url: `${baseUrl}/assets/images/newHome/Home-logo.png`,
            width: 400,
            height: 400,
            alt: 'Thought Pudding Logo',
            type: 'image/png',
          }
        ],
      },

      // Twitter Card tags
      twitter: {
        card: 'summary_large_image',
        site: '@thoughtpudding',
        creator: '@thoughtpudding',
        title,
        description,
        images: [ogImageUrl],
      },

      // Additional meta tags
      other: {
        // Cache control for social media crawlers
        'Cache-Control': 'no-cache, no-store, must-revalidate',
        'Pragma': 'no-cache',
        'Expires': '0',
        'Last-Modified': new Date().toUTCString(),

        // Explicit Open Graph tags for better compatibility
        'og:title': title,
        'og:description': description,
        'og:image': ogImageUrl, // Use generated image that includes profile image
        'og:image:secure_url': ogImageUrl,
        'og:image:width': '1200',
        'og:image:height': '630',
        'og:image:type': 'image/png',
        'og:image:alt': `${therapistName} - ${therapistDesignation}`,
        'og:url': pageUrl,
        'og:site_name': 'Thought Pudding',
        'og:type': 'profile',
        'og:locale': 'en_US',
        'og:updated_time': new Date().toISOString(), // Force refresh

        // Profile specific tags
        'og:profile:first_name': therapistName.split(' ')[0] || '',
        'og:profile:last_name': therapistName.split(' ').slice(1).join(' ') || '',
        'og:profile:username': therapistName.replace(/\s+/g, '').toLowerCase(),

        // Twitter Card tags
        'twitter:card': 'summary_large_image',
        'twitter:site': '@thoughtpudding',
        'twitter:creator': '@thoughtpudding',
        'twitter:title': title,
        'twitter:description': description,
        'twitter:image': hardcodedImageUrl,
        'twitter:image:alt': `${therapistName} - ${therapistDesignation}`,

        // LinkedIn specific tags
        'linkedin:owner': 'Thought Pudding',

        // Additional image fallbacks for better compatibility
        'image': ogImageUrl,
        'image:secure_url': ogImageUrl,
        'image:type': 'image/png',
        'image:width': '1200',
        'image:height': '630',
        'image:alt': `${therapistName} - ${therapistDesignation}`,

        // App specific tags
        'theme-color': '#6D84FF',
        'msapplication-TileColor': '#6D84FF',
        'application-name': 'Thought Pudding',
        'apple-mobile-web-app-title': 'Thought Pudding',
        'apple-mobile-web-app-capable': 'yes',
        'apple-mobile-web-app-status-bar-style': 'default',
        'format-detection': 'telephone=no',
      },

      // Robots and indexing
      robots: {
        index: true,
        follow: true,
        googleBot: {
          index: true,
          follow: true,
          'max-video-preview': -1,
          'max-image-preview': 'large',
          'max-snippet': -1,
        },
      },

      // Verification tags (if needed)
      verification: {
        google: process.env.GOOGLE_VERIFICATION_ID,
      },
    };
  } catch (error) {
    console.error('Error generating metadata for therapist profile:', error);

    // Fallback metadata if therapist data fetch fails
    const fallbackBaseUrl = process.env.NEXT_PUBLIC_BASE_URL || 'https://app.thoughtpudding.com';
    const fallbackCacheVersion = '2025-01-29-v3';
    const fallbackOgImage = `${fallbackBaseUrl}/api/og?name=Professional%20Therapist&title=Licensed%20Therapist&location=Online&image=&v=${fallbackCacheVersion}`;

    return {
      title: 'Book therapy with Professional Therapist - Licensed Therapist',
      description: 'Professional therapy services. Secure, confidential, and professional mental health sessions.',
      openGraph: {
        title: 'Book therapy with Professional Therapist - Licensed Therapist',
        description: 'Professional therapy services. Secure, confidential, and professional mental health sessions.',
        url: `${fallbackBaseUrl}/clients/${id}`,
        siteName: 'Thought Pudding',
        type: 'website',
        images: [
          {
            url: fallbackOgImage,
            width: 1200,
            height: 630,
            alt: 'Thought Pudding - Professional Therapy Platform',
          }
        ],
      },
      twitter: {
        card: 'summary_large_image',
        site: '@thoughtpudding',
        title: 'Book therapy with Professional Therapist - Licensed Therapist',
        description: 'Professional therapy services. Secure, confidential, and professional mental health sessions.',
        images: [fallbackOgImage],
      },
    };
  }
}

export default function ClientLayout({ children }: Props) {
  return <>{children}</>;
}
