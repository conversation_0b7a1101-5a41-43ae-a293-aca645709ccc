"use client";
import React, { useState, useEffect } from "react";
import DashboardLayout from "@/layout/dashboard/DashboardLayout";
import { tabs, tabRedirects } from "../tabs/tabsData";
import { useRouter } from "next/navigation";
import { isTabCompleted, markTabAsCompleted } from "@/utils/completedTabs";
import { Plus, Trash, PencilSimple } from "@phosphor-icons/react";
import TimePicker from "@/components/common/TimePicker";
import DatePickerModal from "@/components/common/DatePickerModal";
import DurationSelector from "@/components/common/DurationSelector";
import Loader from "@/components/common/Loader";
import AnimatedButton from "@/components/common/AnimatedButton";
import TransitionLoader from "@/components/common/TransitionLoader";
import {
  WorkingHoursData,
  TimeSlot,
  saveWorkingHours,
  updateWorkingHours,
  fetchWorkingHours,
  updateSpecificWorkingHour,
  saveSpecificWorkingHour,
  deleteSpecificWorkingHour,
} from "@/services/working-hours.service";

// Define interface for specific day with _id in the UI format
interface SpecificDayWithId {
  date: string;
  timeSlots: TimeSlot[];
  _id: string;
}

// Helper function to format time to 12-hour format
const formatTimeFor12Hour = (time: string): string => {
  if (!time) return "";

  const [hour, minute] = time.split(":").map(Number);
  const period = hour >= 12 ? "PM" : "AM";
  const hour12 = hour % 12 || 12;
  return `${hour12}:${minute < 10 ? "0" + minute : minute} ${period}`;
};

const WorkingHoursPage = () => {
  const router = useRouter();
  const [activeTab, setActiveTab] = useState("Setup Working Hours");
  const [isLoading, setIsLoading] = useState(false);
  const [isDataLoading, setIsDataLoading] = useState(true);
  const [saveSuccess, setSaveSuccess] = useState(false);
  const [redirectCountdown, setRedirectCountdown] = useState(0);

  // Initialize working hours data with default values
  const [workingHoursData, setWorkingHoursData] = useState<WorkingHoursData>({
    weeklyHours: {
      Sun: { isAvailable: false, timeSlots: [] },
      Mon: { isAvailable: false, timeSlots: [] }, // Monday - not available by default
      Tue: {
        isAvailable: true,
        timeSlots: [{ startTime: "08:00", endTime: "08:15", duration: 15 }],
      },
      Wed: {
        isAvailable: true,
        timeSlots: [
          { startTime: "08:00", endTime: "08:15", duration: 15 },
          { startTime: "14:00", endTime: "14:15", duration: 15 },
        ],
      },
      Thu: {
        isAvailable: true,
        timeSlots: [{ startTime: "08:00", endTime: "08:15", duration: 15 }],
      },
      Fri: {
        isAvailable: true,
        timeSlots: [{ startTime: "08:00", endTime: "08:15", duration: 15 }],
      },
      Sat: {
        isAvailable: true, // Saturday - available by default
        timeSlots: [{ startTime: "08:00", endTime: "08:15", duration: 15 }],
      },
    },
    specificDays: [],
  });

  // State for date picker modal
  const [isDatePickerModalOpen, setIsDatePickerModalOpen] = useState(false);
  const [editingDate, setEditingDate] = useState<string | undefined>(undefined);
  const [editingTimeSlots, setEditingTimeSlots] = useState<
    TimeSlot[] | undefined
  >(undefined);
  const [editingSpecificDayId, setEditingSpecificDayId] = useState<
    string | undefined
  >(undefined);

  // State for duration (default to 15 minutes)
  const [durations, setDurations] = useState<{
    [key: string]: { [key: number]: number };
  }>({
    Sun: {},
    Mon: {}, // Monday - not available by default, so no durations
    Tue: { 0: 15 },
    Wed: { 0: 15, 1: 15 },
    Thu: { 0: 15 },
    Fri: { 0: 15 },
    Sat: { 0: 15 }, // Saturday - available by default with duration
  });

  // State to track if we have existing data (for update vs create)
  const [hasExistingData, setHasExistingData] = useState(false);

  // Fetch working hours data when component mounts
  useEffect(() => {
    const getWorkingHours = async () => {
      try {
        setIsDataLoading(true);
        const result = await fetchWorkingHours();
        setWorkingHoursData(result.data);
        setHasExistingData(result.hasExistingData);
      } catch (error) {
        console.error("Error fetching working hours:", error);
        setHasExistingData(false);
        // Default data is already set in the state initialization
      } finally {
        setIsDataLoading(false);
      }
    };

    getWorkingHours();
  }, []);

  // Initialize durations state from workingHoursData when it changes
  useEffect(() => {
    if (!isDataLoading) {
      // Create a new durations object based on the time slots in workingHoursData
      const newDurations: { [key: string]: { [key: number]: number } } = {
        Sun: {},
        Mon: {},
        Tue: {},
        Wed: {},
        Thu: {},
        Fri: {},
        Sat: {},
      };

      // Populate the durations from the time slots
      Object.entries(workingHoursData.weeklyHours).forEach(([day, dayData]) => {
        dayData.timeSlots.forEach((slot, index) => {
          // Use the duration from the time slot if available, otherwise calculate it
          if (slot.duration) {
            newDurations[day][index] = slot.duration;
          } else {
            // Calculate duration from start and end times
            const [startHour, startMinute] = slot.startTime
              .split(":")
              .map(Number);
            const [endHour, endMinute] = slot.endTime.split(":").map(Number);

            const startTimeInMinutes = startHour * 60 + startMinute;
            let endTimeInMinutes = endHour * 60 + endMinute;

            // Handle case where end time is on the next day
            if (endTimeInMinutes < startTimeInMinutes) {
              endTimeInMinutes += 24 * 60; // Add 24 hours in minutes
            }

            newDurations[day][index] = endTimeInMinutes - startTimeInMinutes;
          }
        });
      });

      setDurations(newDurations);
    }
  }, [workingHoursData, isDataLoading]);

  // Handle day availability toggle
  const handleDayAvailabilityChange = (day: string) => {
    setWorkingHoursData((prev) => ({
      ...prev,
      weeklyHours: {
        ...prev.weeklyHours,
        [day]: {
          ...prev.weeklyHours[day],
          isAvailable: !prev.weeklyHours[day].isAvailable,
          timeSlots: prev.weeklyHours[day].isAvailable
            ? []
            : [{ startTime: "08:00", endTime: "08:15", duration: 15 }],
        },
      },
    }));
  };

  // Handle time slot changes
  const handleTimeChange = (
    day: string,
    index: number,
    field: "startTime" | "endTime",
    value: string
  ) => {
    const updatedTimeSlots = [...workingHoursData.weeklyHours[day].timeSlots];

    if (field === "startTime") {
      // When start time changes, recalculate end time based on duration
      const duration = durations[day][index] || 15;

      // Parse the start time
      const [startHour, startMinute] = value.split(":").map(Number);
      const startTimeInMinutes = startHour * 60 + startMinute;

      // Calculate end time
      const totalMinutes = startTimeInMinutes + duration;
      const hour = Math.floor(totalMinutes / 60) % 24;
      const minute = totalMinutes % 60;
      const formattedHour = hour < 10 ? `0${hour}` : `${hour}`;
      const formattedMinute = minute < 10 ? `0${minute}` : `${minute}`;
      const endTime = `${formattedHour}:${formattedMinute}`;

      updatedTimeSlots[index] = {
        startTime: value,
        endTime: endTime,
        duration: duration,
      };
    } else {
      // For end time, update the value and recalculate duration
      const slot = updatedTimeSlots[index];
      const [startHour, startMinute] = slot.startTime.split(":").map(Number);
      const [endHour, endMinute] = value.split(":").map(Number);

      const startTimeInMinutes = startHour * 60 + startMinute;
      let endTimeInMinutes = endHour * 60 + endMinute;

      // Handle case where end time is on the next day
      if (endTimeInMinutes < startTimeInMinutes) {
        endTimeInMinutes += 24 * 60; // Add 24 hours in minutes
      }

      const calculatedDuration = endTimeInMinutes - startTimeInMinutes;

      updatedTimeSlots[index] = {
        ...slot,
        endTime: value,
        duration: calculatedDuration,
      };

      // Also update the durations state
      setDurations((prev) => ({
        ...prev,
        [day]: {
          ...prev[day],
          [index]: calculatedDuration,
        },
      }));
    }

    setWorkingHoursData((prev) => ({
      ...prev,
      weeklyHours: {
        ...prev.weeklyHours,
        [day]: {
          ...prev.weeklyHours[day],
          timeSlots: updatedTimeSlots,
        },
      },
    }));
  };

  // Handle duration changes
  const handleDurationChange = (
    day: string,
    index: number,
    duration: number
  ) => {
    // Update the duration state
    setDurations((prev) => ({
      ...prev,
      [day]: {
        ...prev[day],
        [index]: duration,
      },
    }));

    // Update the end time based on the new duration
    const { startTime } = workingHoursData.weeklyHours[day].timeSlots[index];

    // Parse the start time
    const [startHour, startMinute] = startTime.split(":").map(Number);
    const startTimeInMinutes = startHour * 60 + startMinute;

    // Calculate end time
    const totalMinutes = startTimeInMinutes + duration;
    const hour = Math.floor(totalMinutes / 60) % 24;
    const minute = totalMinutes % 60;
    const formattedHour = hour < 10 ? `0${hour}` : `${hour}`;
    const formattedMinute = minute < 10 ? `0${minute}` : `${minute}`;
    const endTime = `${formattedHour}:${formattedMinute}`;

    // Update the time slot with duration
    const updatedTimeSlots = [...workingHoursData.weeklyHours[day].timeSlots];
    updatedTimeSlots[index] = {
      ...updatedTimeSlots[index],
      endTime: endTime,
      duration: duration,
    };

    setWorkingHoursData((prev) => ({
      ...prev,
      weeklyHours: {
        ...prev.weeklyHours,
        [day]: {
          ...prev.weeklyHours[day],
          timeSlots: updatedTimeSlots,
        },
      },
    }));
  };

  // Add a new time slot for a day
  const addTimeSlot = (day: string) => {
    const newIndex = workingHoursData.weeklyHours[day].timeSlots.length;

    // Set default duration for the new time slot
    setDurations((prev) => ({
      ...prev,
      [day]: {
        ...prev[day],
        [newIndex]: 15,
      },
    }));

    // Add the new time slot with calculated end time and duration
    setWorkingHoursData((prev) => ({
      ...prev,
      weeklyHours: {
        ...prev.weeklyHours,
        [day]: {
          ...prev.weeklyHours[day],
          timeSlots: [
            ...prev.weeklyHours[day].timeSlots,
            { startTime: "08:00", endTime: "08:15", duration: 15 },
          ],
        },
      },
    }));
  };

  // Remove a time slot for a day
  const removeTimeSlot = (day: string, index: number) => {
    const updatedTimeSlots = [...workingHoursData.weeklyHours[day].timeSlots];
    updatedTimeSlots.splice(index, 1);

    // Update the working hours data
    setWorkingHoursData((prev) => ({
      ...prev,
      weeklyHours: {
        ...prev.weeklyHours,
        [day]: {
          ...prev.weeklyHours[day],
          timeSlots: updatedTimeSlots,
        },
      },
    }));

    // Update the durations object by removing the entry and reindexing
    const updatedDurations = { ...durations[day] };
    delete updatedDurations[index];

    // Reindex the durations
    const newDurations: { [key: number]: number } = {};
    updatedTimeSlots.forEach((_, i) => {
      if (i < index) {
        newDurations[i] = updatedDurations[i] || 15;
      } else {
        newDurations[i] = updatedDurations[i + 1] || 15;
      }
    });

    setDurations((prev) => ({
      ...prev,
      [day]: newDurations,
    }));
  };

  // Handle form submission
  const handleSubmit = async () => {
    setIsLoading(true);
    try {
      // Create a copy of the working hours data with only the weekly hours
      // This prevents sending specific days again, which would cause conflicts
      const weeklyHoursOnly = {
        weeklyHours: { ...workingHoursData.weeklyHours },
        specificDays: [], // Empty array to avoid sending specific days again
      };

      // Use hasExistingData to determine if we should update or create
      if (hasExistingData) {
        // Update existing working hours using PATCH (weekly hours only)
        await updateWorkingHours(weeklyHoursOnly);
      } else {
        // Create new working hours using POST (weekly hours only)
        await saveWorkingHours(weeklyHoursOnly);
      }

      // Set success state
      setSaveSuccess(true);

      // Mark this tab as completed
      markTabAsCompleted(activeTab);

      // Start countdown for redirection (3 seconds)
      setRedirectCountdown(3);

      // Set up countdown timer
      const countdownInterval = setInterval(() => {
        setRedirectCountdown((prev) => {
          if (prev <= 1) {
            clearInterval(countdownInterval);
            setIsLoading(false);

            // Navigate to next tab or page when countdown reaches 0
            const currentTabIndex = tabs.indexOf(activeTab);
            if (currentTabIndex < tabs.length - 1) {
              // Automatically redirect to the next tab
              router.push(
                tabRedirects[
                  tabs[currentTabIndex + 1] as keyof typeof tabRedirects
                ]
              );
            }
            return 0;
          }
          return prev - 1;
        });
      }, 1000);
    } catch (error) {
      console.error("Error submitting working hours:", error);
      setIsLoading(false);
    }
  };

  // Open the date picker modal for adding a new specific day
  const addSpecificDay = () => {
    setEditingDate(undefined);
    setEditingTimeSlots(undefined);
    setEditingSpecificDayId(undefined);
    setIsDatePickerModalOpen(true);
  };

  // Open the date picker modal for editing an existing specific day
  const editSpecificDay = (date: string) => {
    const dayToEdit = workingHoursData.specificDays.find(
      (day) => day.date === date
    );

    if (dayToEdit) {
      // Check if the day has an _id property (it should come from the API)
      const specificDayId = (dayToEdit as unknown as SpecificDayWithId)._id;

      if (!specificDayId) {
        return;
      }

      // Format the date if needed
      let formattedDate = dayToEdit.date;

      // If the date is not in "DD, Month, YYYY" format, try to convert it
      if (!formattedDate.includes(", ")) {
        try {
          // Try to parse the date
          let dateObj: Date;

          if (formattedDate.includes("-")) {
            // Format: "DD-MM-YYYY" or similar
            const parts = formattedDate.split("-");
            if (parts.length === 3) {
              // Assuming day-month-year format
              dateObj = new Date(
                parseInt(parts[2]),
                parseInt(parts[1]) - 1,
                parseInt(parts[0])
              );

              // Format as "DD, Month, YYYY"
              const months = [
                "January",
                "February",
                "March",
                "April",
                "May",
                "June",
                "July",
                "August",
                "September",
                "October",
                "November",
                "December",
              ];
              formattedDate = `${dateObj.getDate()}, ${
                months[dateObj.getMonth()]
              }, ${dateObj.getFullYear()}`;
            }
          }
        } catch (e) {
          console.error("Error formatting date for edit:", e);
          // Keep the original date if parsing fails
        }
      }

      setEditingDate(formattedDate);
      setEditingTimeSlots(dayToEdit.timeSlots);
      setEditingSpecificDayId(specificDayId);
      setIsDatePickerModalOpen(true);
    }
  };

  // Handle saving specific day schedule
  const handleSaveSpecificDay = async (
    date: string,
    timeSlots: TimeSlot[],
    specificDayId?: string
  ) => {

    // Format the date to DD-MM-YYYY format as required by the API
    let formattedDate = date;

    // Check if the date is in "DD, Month, YYYY" format
    if (date.includes(", ")) {
      formattedDate = formatDateForAPI(date);
    }

    // formatDateForAPI already returns the correct DD-MM-YYYY format, so no additional conversion needed

    // Ensure all time slots have duration
    const timeSlotsWithDuration = timeSlots.map((slot) => {
      if (slot.duration) return slot;

      // Calculate duration if not present
      const [startHour, startMinute] = slot.startTime.split(":").map(Number);
      const [endHour, endMinute] = slot.endTime.split(":").map(Number);

      const startTimeInMinutes = startHour * 60 + startMinute;
      let endTimeInMinutes = endHour * 60 + endMinute;

      // Handle case where end time is on the next day
      if (endTimeInMinutes < startTimeInMinutes) {
        endTimeInMinutes += 24 * 60; // Add 24 hours in minutes
      }

      return {
        ...slot,
        duration: endTimeInMinutes - startTimeInMinutes,
      };
    });

    // If we have a specific day ID, update it directly using PATCH
    if (specificDayId) {
      try {
        // Prepare data for the API
        const specificDayData = {
          date: formattedDate,
          slots: timeSlotsWithDuration.map((slot) => {
            // eslint-disable-next-line @typescript-eslint/no-unused-vars
            const { apiDate, ...slotWithoutApiDate } = slot as TimeSlot & {
              apiDate?: string;
            };
            // eslint-disable-next-line @typescript-eslint/no-unused-vars
            const { _id, ...slotWithoutId } = slotWithoutApiDate as TimeSlot & {
              _id?: string;
            };
            return slotWithoutId;
          }),
        };

        // Update the specific day using PATCH
        await updateSpecificWorkingHour(specificDayId, specificDayData);

        // Immediately update the local state with the updated specific day
        // This ensures the UI shows the updated data right away
        const updatedSpecificDay = {
          date: date, // Use the original formatted date for UI display
          timeSlots: timeSlotsWithDuration,
          _id: specificDayId // Keep the existing ID
        };

        // Update the local state immediately
        setWorkingHoursData(prev => ({
          ...prev,
          specificDays: prev.specificDays.map(day =>
            (day as unknown as SpecificDayWithId)._id === specificDayId
              ? updatedSpecificDay
              : day
          )
        }));

        // Also refresh the working hours data to ensure consistency with backend
        // This is done asynchronously to not block the UI update
        setTimeout(async () => {
          try {
            const { data } = await fetchWorkingHours();
            setWorkingHoursData(data);
          } catch (error) {
            console.error("Error refreshing working hours data:", error);
          }
        }, 100);

        return;
      } catch (error) {
        console.error("Error updating specific day:", error);
      }
    }

    // Prepare the specific day data
    const specificDay = {
      date, // Keep original format for UI
      timeSlots: timeSlotsWithDuration.map((slot) => {
        // eslint-disable-next-line @typescript-eslint/no-unused-vars
        const { _id, ...slotWithoutId } = slot as TimeSlot & { _id?: string };
        return {
          ...slotWithoutId,
          apiDate: formattedDate,
        };
      }),
    };

    try {
      // Save the specific day directly to the backend
      const response = await saveSpecificWorkingHour(specificDay);

      // Immediately update the local state with the new specific day
      // This ensures the UI shows the new data right away
      if (response && response.status === "success" && response.data) {

        // Use the actual slots from the API response, not the original timeSlots
        // This ensures we get the correct data including any backend modifications
        const apiSlots = response.data.slots || [];
        const convertedTimeSlots = (apiSlots as Array<TimeSlot & { duration: number }>).map((slot) => ({
          startTime: slot.startTime,
          endTime: slot.endTime,
          duration: slot.duration
        }));

        const newSpecificDay = {
          date: date, // Use the original formatted date for UI display
          timeSlots: convertedTimeSlots, // Use the slots from API response
          _id: response.data._id // Include the ID from the response
        };


        // Update the local state immediately
        setWorkingHoursData(prev => {
          const updatedData = {
            ...prev,
            specificDays: [...prev.specificDays, newSpecificDay]
          };
          return updatedData;
        });
      }

      // Also refresh the working hours data to ensure consistency with backend
      // This is done asynchronously to not block the UI update
      setTimeout(async () => {
        try {
          const { data } = await fetchWorkingHours();

          // Only update the state if the refresh was successful AND we got meaningful data
          // Check if the new specific day is included in the refreshed data
          const refreshedSpecificDay = data.specificDays.find(day => day._id === response.data._id);
          if (refreshedSpecificDay) {
            setWorkingHoursData(data);
          }
        } catch (error) {
          console.error("❌ Error refreshing working hours data:", error);
          // Don't update the state if there's an error, keep the local state
        }
      }, 100);

    } catch (error) {
      console.error("Error saving specific day:", error);
    }
  };

  // Format date from "DD, Month, YYYY" to "DD-MM-YYYY" format as required by the backend
  const formatDateForAPI = (dateString: string): string => {
    const [day, month, year] = dateString.split(", ");
    const monthIndex =
      [
        "January",
        "February",
        "March",
        "April",
        "May",
        "June",
        "July",
        "August",
        "September",
        "October",
        "November",
        "December",
      ].indexOf(month) + 1;

    const formattedMonth = monthIndex < 10 ? `0${monthIndex}` : `${monthIndex}`;
    const formattedDay = parseInt(day) < 10 ? `0${day}` : day;

    // Format as DD-MM-YYYY as required by the backend
    return `${formattedDay}-${formattedMonth}-${year}`;
  };

  // Handle deleting a specific time slot
  const handleDeleteSpecificTimeSlot = async (
    date: string,
    slotIndex: number,
    specificDayId: string,
    timeSlots: TimeSlot[]
  ) => {
    if (!specificDayId) {
      return;
    }

    try {
      // If this is the last time slot, remove the entire specific day
      if (timeSlots.length === 1) {
        // Format the date properly for the API
        let formattedDate = date;

        // Check if the date is in "DD, Month, YYYY" format
        if (date.includes(", ")) {
          formattedDate = formatDateForAPI(date);
        }

        // formatDateForAPI already returns the correct DD-MM-YYYY format, so no additional conversion needed

        await deleteSpecificWorkingHour(specificDayId, formattedDate);

        // Refresh the working hours data to get the updated list from the backend
        const { data } = await fetchWorkingHours();
        setWorkingHoursData(data);
        return;
      }

      // Format the date properly for the API
      let formattedDate = date;

      // Check if the date is in "DD, Month, YYYY" format
      if (date.includes(", ")) {
        formattedDate = formatDateForAPI(date);
      }

      // formatDateForAPI already returns the correct DD-MM-YYYY format, so no additional conversion needed

      // Create a copy of the slots array and remove the slot at the specified index
      const updatedSlots = [...timeSlots];
      updatedSlots.splice(slotIndex, 1);

      // Prepare the data for the PATCH request
      const specificDayData = {
        date: formattedDate,
        slots: updatedSlots.map((slot) => {
          // eslint-disable-next-line @typescript-eslint/no-unused-vars
          const { _id, apiDate, ...slotWithoutId } = slot as TimeSlot & {
            _id?: string;
            apiDate?: string;
          };

          // Add duration if it doesn't exist
          if (!slotWithoutId.duration) {
            const [startHour, startMinute] = slotWithoutId.startTime
              .split(":")
              .map(Number);
            const [endHour, endMinute] = slotWithoutId.endTime
              .split(":")
              .map(Number);

            const startTimeInMinutes = startHour * 60 + startMinute;
            let endTimeInMinutes = endHour * 60 + endMinute;

            if (endTimeInMinutes < startTimeInMinutes) {
              endTimeInMinutes += 24 * 60;
            }

            slotWithoutId.duration = endTimeInMinutes - startTimeInMinutes;
          }

          return slotWithoutId;
        }),
      };

      // Use the updateSpecificWorkingHour function to send the PATCH request
      await updateSpecificWorkingHour(specificDayId, specificDayData);

      // Refresh the working hours data to get the updated list from the backend
      const { data } = await fetchWorkingHours();
      setWorkingHoursData(data);
    } catch (error) {
      console.error("Error removing time slot:", error);
      if (error && typeof error === "object" && "response" in error) {
        type AxiosErrorType = {
          response?: {
            data: unknown;
            status: number;
          };
        };
        const axiosError = error as AxiosErrorType;
        if (axiosError.response) {
          console.error("Error response data:", axiosError.response.data);
          console.error("Error response status:", axiosError.response.status);
        }
      }
    }
  };

  // Remove a specific day schedule
  const removeSpecificDay = async (date: string) => {
    // Find the specific day to get its ID
    const dayToRemove = workingHoursData.specificDays.find(
      (day) => day.date === date
    );

    if (!dayToRemove) {
      return;
    }

    // Check if the day has an _id property (it should come from the API)
    const specificDayId = (dayToRemove as SpecificDayWithId)._id;

    if (!specificDayId) {
      // If there's no ID, just remove it from the local state
      setWorkingHoursData((prev) => ({
        ...prev,
        specificDays: prev.specificDays.filter((day) => day.date !== date),
      }));

      return;
    }

    try {
      // Format the date properly for the API
      let formattedDate = date;

      // Check if the date is in "DD, Month, YYYY" format
      if (date.includes(", ")) {
        formattedDate = formatDateForAPI(date);
      }

      // formatDateForAPI already returns the correct DD-MM-YYYY format, so no additional conversion needed

      // Use the PATCH endpoint with a dummy slot to effectively delete the specific day
      await deleteSpecificWorkingHour(specificDayId, formattedDate);

      // Refresh the working hours data to get the updated list from the backend
      const { data } = await fetchWorkingHours();
      setWorkingHoursData(data);
    } catch (error) {
      console.error("Error removing specific day:", error);
    }
  };

  // Calculate progress percentage for the transition loader
  const calculateProgress = () => {
    if (!saveSuccess) return 0;
    return ((3 - redirectCountdown) / 3) * 100;
  };

  return (
    <DashboardLayout>
      {/* Transition loader - shown during saving and redirection */}
      <TransitionLoader
        isVisible={saveSuccess}
        message="Working hours saved successfully!"
        redirectMessage="Redirecting to Booking Message..."
        progress={calculateProgress()}
      />
      <div className="p-0">
        {/* White background container with rounded corners */}
        <div className="bg-white rounded-xl shadow-sm">
          {/* Tabs */}
          <div className="mb-8">
            <div className="flex overflow-x-auto no-scrollbar mt-4">
              {tabs.map((tab, index) => {
                // Determine if this tab should be disabled
                const currentTabIndex = tabs.indexOf(activeTab);
                // Check if the tab is completed or is the current tab
                const isCompleted = isTabCompleted(tab);
                const isDisabled = index > currentTabIndex && !isCompleted;

                return (
                  <button
                    key={tab}
                    className={`px-16 py-4 whitespace-nowrap ${
                      activeTab === tab
                        ? "border-b-2 border-yellow-600 text-yellow-600 font-medium"
                        : isDisabled
                        ? "text-gray-300 cursor-not-allowed"
                        : "text-gray-500"
                    }`}
                    onClick={() => {
                      if (!isDisabled) {
                        if (tab === "Setup Working Hours") {
                          setActiveTab(tab); // Stay on current page
                        } else {
                          // Navigate to the corresponding page
                          router.push(
                            tabRedirects[tab as keyof typeof tabRedirects]
                          );
                        }
                      }
                    }}
                    disabled={isDisabled}
                  >
                    {tab}
                  </button>
                );
              })}
            </div>
          </div>

          <div className="p-4 md:p-6">
            {isDataLoading ? (
              <div className="flex justify-center items-center h-64">
                <Loader size="large" text="Loading working hours..." />
              </div>
            ) : (
              <div className="mb-6">
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                  {/* Weekly Work Hours Section */}
                  <div>
                    <h2 className="text-xl font-semibold mb-4">
                      Weekly Work Hours
                    </h2>
                    <div className="space-y-4 w-full">
                      {Object.entries(workingHoursData.weeklyHours).map(
                        ([day, schedule]) => (
                          <div key={day} className="border-b pb-4 mb-4">
                            <div className="flex flex-col sm:flex-row sm:items-start">
                              <div className="w-full sm:w-20 flex items-center pt-3 mb-2 sm:mb-0">
                                <input
                                  type="checkbox"
                                  id={`day-${day}`}
                                  checked={schedule.isAvailable}
                                  onChange={() =>
                                    handleDayAvailabilityChange(day)
                                  }
                                  className="mr-2 h-4 w-4 accent-blue-600"
                                />
                                <label
                                  htmlFor={`day-${day}`}
                                  className="font-medium text-gray-700"
                                >
                                  {day}
                                </label>
                              </div>

                              {schedule.isAvailable ? (
                                <div className="flex-1 w-full">
                                  <div className="space-y-3">
                                    {schedule.timeSlots.map((slot, index) => (
                                      <div
                                        key={index}
                                        className="flex flex-col sm:flex-row items-start sm:items-center sm:space-x-2 space-y-2 sm:space-y-0 flex-wrap"
                                      >
                                        <div className="flex flex-1 flex-wrap items-center gap-2 sm:gap-3 md:gap-4 min-w-0 mr-2">
                                          <TimePicker
                                            value={slot.startTime}
                                            onChange={(value: string) =>
                                              handleTimeChange(
                                                day,
                                                index,
                                                "startTime",
                                                value
                                              )
                                            }
                                            className="w-full sm:w-28 md:w-32 lg:w-36"
                                          />
                                          <DurationSelector
                                            value={durations[day][index] || 15}
                                            onChange={(duration: number) =>
                                              handleDurationChange(
                                                day,
                                                index,
                                                duration
                                              )
                                            }
                                            className="w-full sm:w-32 md:w-36 lg:w-40"
                                          />
                                          <span className="hidden sm:block text-gray-500">
                                            -
                                          </span>
                                          <div className="w-full sm:w-28 md:w-32 lg:w-36">
                                            <input
                                              type="text"
                                              value={formatTimeFor12Hour(slot.endTime)}
                                              disabled={true}
                                              placeholder="12:00 AM"
                                              className="w-full text-sm py-3 px-2.5 border rounded-lg outline-none bg-gray-100 cursor-not-allowed text-gray-700 border-[#D9D9D9]"
                                              style={{ minHeight: '42px' }}
                                            />
                                          </div>
                                        </div>

                                        <div className="flex space-x-2 ml-auto sm:ml-0">
                                          {index ===
                                          schedule.timeSlots.length - 1 ? (
                                            <button
                                              onClick={() => addTimeSlot(day)}
                                              className="p-2 text-blue-600 hover:bg-blue-50 rounded-full transition-colors"
                                              title="Add another time slot"
                                            >
                                              <Plus size={20} />
                                            </button>
                                          ) : (
                                            <button
                                              onClick={() =>
                                                removeTimeSlot(day, index)
                                              }
                                              className="p-2 text-red-600 hover:bg-red-50 rounded-full transition-colors"
                                              title="Remove this time slot"
                                            >
                                              <Trash size={20} />
                                            </button>
                                          )}
                                        </div>
                                      </div>
                                    ))}
                                  </div>
                                </div>
                              ) : (
                                <div className="text-gray-500 italic ml-0 sm:ml-2 pt-3">
                                  Not Available On This Day
                                </div>
                              )}
                            </div>
                          </div>
                        )
                      )}
                    </div>
                  </div>

                  {/* Set Specific Days Hours Section */}
                  <div>
                    <h2 className="text-xl font-semibold mb-2">
                      Set Specific Days Hours
                    </h2>
                    <p className="text-gray-600 mb-6">
                      Easily Set Custom Hours For Special Dates To Accommodate
                      Exceptions To Your Standard Schedule.
                    </p>

                    <button
                      onClick={addSpecificDay}
                      className="flex items-center justify-center w-full py-3 px-4 border border-blue-600 text-blue-600 rounded-lg hover:bg-blue-50 transition-colors bg-white shadow-sm"
                    >
                      <Plus size={20} className="mr-2" />
                      Add Your Specific Hours
                    </button>

                    {/* Specific Days Table */}
                    <div className="mt-6 border rounded-xl p-4 md:p-6">
                      <div className="grid grid-cols-3 gap-2 md:gap-4 font-medium text-gray-700 mb-4 px-2 md:px-4">
                        <div className="text-sm md:text-base">DATE</div>
                        <div className="text-sm md:text-base">TIME</div>
                        <div className="text-right text-sm md:text-base">
                          ACTION
                        </div>
                      </div>

                      <div className="space-y-4">
                        {workingHoursData.specificDays.length > 0 ? (
                          workingHoursData.specificDays.map((day, index) => (
                            <div
                              key={index}
                              className="grid grid-cols-3 gap-2 md:gap-4 items-center bg-white p-2 md:p-4 rounded-lg border border-gray-100"
                            >
                              <div className="text-sm md:text-base overflow-hidden text-ellipsis">
                                {day.date}
                              </div>
                              <div>
                                {day.timeSlots.map((slot, slotIndex) => (
                                  <div
                                    key={slotIndex}
                                    className="text-sm md:text-base flex items-center"
                                  >
                                    <span>
                                      {formatTimeFor12Hour(slot.startTime)} -{" "}
                                      {formatTimeFor12Hour(slot.endTime)}
                                    </span>
                                    <button
                                      className="ml-2 p-1 text-red-600 hover:bg-red-50 rounded-full transition-colors"
                                      onClick={(e) => {
                                        e.stopPropagation();
                                        handleDeleteSpecificTimeSlot(
                                          day.date,
                                          slotIndex,
                                          (day as SpecificDayWithId)._id,
                                          day.timeSlots
                                        );
                                      }}
                                      title="Remove this time slot"
                                    >
                                      <Trash size={12} />
                                    </button>
                                  </div>
                                ))}
                              </div>
                              <div className="flex justify-end space-x-2">
                                <button
                                  className="p-2 text-blue-600 hover:bg-blue-50 rounded-full transition-colors"
                                  onClick={() => editSpecificDay(day.date)}
                                  title="Edit this specific day"
                                >
                                  <PencilSimple
                                    size={16}
                                    className="md:w-5 md:h-5"
                                  />
                                </button>
                                <button
                                  className="p-2 text-red-600 hover:bg-red-50 rounded-full transition-colors"
                                  onClick={() => removeSpecificDay(day.date)}
                                  title="Remove this specific day"
                                >
                                  <Trash size={16} className="md:w-5 md:h-5" />
                                </button>
                              </div>
                            </div>
                          ))
                        ) : (
                          <div className="text-center py-4 text-gray-500">
                            No specific days added yet. Click the button above
                            to add custom hours.
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                </div>

                {/* Continue Button */}
                <div className="mt-8 text-start">
                  <AnimatedButton
                    onClick={handleSubmit}
                    isLoading={isLoading}
                    isSuccess={saveSuccess}
                    disabled={isLoading || saveSuccess}
                    loadingText="Saving..."
                    successText="Redirecting..."
                    className="w-[250px] h-[48px]"
                  >
                    Continue
                  </AnimatedButton>
                </div>
              </div>
            )}

            {/* Date Picker Modal */}
            <DatePickerModal
              open={isDatePickerModalOpen}
              onClose={() => {
                setIsDatePickerModalOpen(false);
                setEditingDate(undefined);
                setEditingTimeSlots(undefined);
                setEditingSpecificDayId(undefined);
              }}
              onSave={handleSaveSpecificDay}
              editDate={editingDate}
              editTimeSlots={editingTimeSlots}
              specificDayId={editingSpecificDayId}
            />
          </div>
        </div>
      </div>
    </DashboardLayout>
  );
};

export default WorkingHoursPage;
